from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.auth import get_user_model

User = get_user_model()


class FrequencyBand(models.Model):
    """Modèle pour les bandes de fréquences"""
    
    BAND_TYPES = [
        ('HF', 'Haute Fréquence (3-30 MHz)'),
        ('VHF', 'Très Haute Fréquence (30-300 MHz)'),
        ('UHF', 'Ultra Haute Fréquence (300-3000 MHz)'),
        ('SHF', 'Super Haute Fréquence (3-30 GHz)'),
    ]
    
    name = models.CharField(
        max_length=10,
        choices=BAND_TYPES,
        unique=True,
        verbose_name='Type de bande'
    )
    
    min_frequency = models.FloatField(
        verbose_name='Fréquence minimale (MHz)',
        validators=[MinValueValidator(0.001)]
    )
    
    max_frequency = models.FloatField(
        verbose_name='Fréquence maximale (MHz)',
        validators=[MinValueValidator(0.001)]
    )
    
    description = models.TextField(
        blank=True,
        verbose_name='Description'
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name='Bande active'
    )
    
    class Meta:
        verbose_name = 'Bande de fréquence'
        verbose_name_plural = 'Bandes de fréquences'
        ordering = ['min_frequency']
    
    def __str__(self):
        return f"{self.name} ({self.min_frequency}-{self.max_frequency} MHz)"
    
    def frequency_range(self):
        """Retourne la plage de fréquences formatée"""
        return f"{self.min_frequency}-{self.max_frequency} MHz"


class Organe(models.Model):
    """Modèle pour les organes/unités militaires"""
    
    ORGANE_TYPES = [
        ('regiment', 'Régiment'),
        ('bataillon', 'Bataillon'),
        ('compagnie', 'Compagnie'),
        ('escadron', 'Escadron'),
        ('groupe', 'Groupe'),
        ('section', 'Section'),
        ('etat_major', 'État-Major'),
        ('service', 'Service'),
        ('autre', 'Autre'),
    ]
    
    name = models.CharField(
        max_length=200,
        verbose_name='Nom de l\'organe'
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='Code organe'
    )
    
    type_organe = models.CharField(
        max_length=20,
        choices=ORGANE_TYPES,
        verbose_name='Type d\'organe'
    )
    
    parent_organe = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='sub_organes',
        verbose_name='Organe parent'
    )
    
    # Coordonnées géographiques
    latitude = models.FloatField(
        null=True,
        blank=True,
        validators=[MinValueValidator(-90), MaxValueValidator(90)],
        verbose_name='Latitude'
    )
    
    longitude = models.FloatField(
        null=True,
        blank=True,
        validators=[MinValueValidator(-180), MaxValueValidator(180)],
        verbose_name='Longitude'
    )
    
    # Informations de contact
    address = models.TextField(
        blank=True,
        verbose_name='Adresse'
    )
    
    phone = models.CharField(
        max_length=20,
        blank=True,
        verbose_name='Téléphone'
    )
    
    email = models.EmailField(
        blank=True,
        verbose_name='Email'
    )
    
    # Informations administratives
    commander = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='Commandant'
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='Date de création'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='Dernière modification'
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name='Organe actif'
    )
    
    class Meta:
        verbose_name = 'Organe'
        verbose_name_plural = 'Organes'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def get_full_hierarchy(self):
        """Retourne la hiérarchie complète de l'organe"""
        hierarchy = [self.name]
        parent = self.parent_organe
        while parent:
            hierarchy.insert(0, parent.name)
            parent = parent.parent_organe
        return ' > '.join(hierarchy)

    def get_type_badge_class(self):
        """Retourne la classe CSS pour le badge du type"""
        type_classes = {
            'regiment': 'bg-primary',
            'bataillon': 'bg-success',
            'compagnie': 'bg-info',
            'escadron': 'bg-warning text-dark',
            'etat_major': 'bg-danger',
        }
        return type_classes.get(self.type_organe, 'bg-secondary')
    
    def has_coordinates(self):
        """Vérifie si l'organe a des coordonnées géographiques"""
        return self.latitude is not None and self.longitude is not None


class Frequency(models.Model):
    """Modèle pour les fréquences"""
    
    STATUS_CHOICES = [
        ('available', 'Disponible'),
        ('assigned', 'Attribuée'),
        ('reserved', 'Réservée'),
        ('maintenance', 'En maintenance'),
        ('blocked', 'Bloquée'),
    ]
    
    NATURE_CHOICES = [
        ('permanent', 'Permanente'),
        ('temporary', 'Provisoire'),
        ('emergency', 'Urgence'),
    ]
    
    TYPE_CHOICES = [
        ('unique', 'Unique'),
        ('bande', 'Bande'),
        ('couple', 'Couple'),
    ]

    STATION_TYPE_CHOICES = [
        ('mobile', 'Mobile'),
        ('fixe', 'Fixe'),
    ]
    
    # Identification
    frequency_value = models.FloatField(
        verbose_name='Valeur de la fréquence (MHz)',
        validators=[MinValueValidator(0.001)]
    )
    
    band = models.ForeignKey(
        FrequencyBand,
        on_delete=models.CASCADE,
        related_name='frequencies',
        verbose_name='Bande de fréquence'
    )
    
    reference = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='Référence'
    )
    
    # Attribution
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='available',
        verbose_name='Statut'
    )
    
    assigned_to = models.ForeignKey(
        Organe,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_frequencies',
        verbose_name='Attribuée à'
    )

    assigned_to_text = models.CharField(
        max_length=200,
        blank=True,
        verbose_name='Organe attribué (texte libre)'
    )
    
    nature = models.CharField(
        max_length=20,
        choices=NATURE_CHOICES,
        default='permanent',
        verbose_name='Nature'
    )
    
    network_type = models.CharField(
        max_length=20,
        choices=NETWORK_CHOICES,
        verbose_name='Type de réseau'
    )
    
    # Dates
    assignment_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Date d\'attribution'
    )
    
    expiry_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Date d\'expiration'
    )
    
    # Paramètres techniques
    latitude = models.FloatField(
        null=True,
        blank=True,
        verbose_name='Latitude'
    )

    longitude = models.FloatField(
        null=True,
        blank=True,
        verbose_name='Longitude'
    )

    power_max = models.FloatField(
        null=True,
        blank=True,
        verbose_name='Puissance maximale (W)'
    )
    
    coverage_radius = models.FloatField(
        null=True,
        blank=True,
        verbose_name='Rayon de couverture (km)'
    )
    
    # Règles de réutilisation
    min_distance_reuse = models.FloatField(
        default=50.0,
        verbose_name='Distance minimale de réutilisation (km)'
    )
    
    # Informations administratives
    notes = models.TextField(
        blank=True,
        verbose_name='Notes'
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='Date de création'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='Dernière modification'
    )
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_frequencies',
        verbose_name='Créée par'
    )
    
    class Meta:
        verbose_name = 'Fréquence'
        verbose_name_plural = 'Fréquences'
        ordering = ['frequency_value']
        unique_together = ['frequency_value', 'band']
    
    def __str__(self):
        return f"{self.frequency_value} MHz ({self.reference})"
    
    def is_available(self):
        """Vérifie si la fréquence est disponible"""
        return self.status == 'available'
    
    def is_expired(self):
        """Vérifie si la fréquence a expiré"""
        if self.expiry_date:
            return timezone.now() > self.expiry_date
        return False
    
    def get_status_badge_class(self):
        """Retourne la classe CSS pour le badge de statut"""
        status_classes = {
            'available': 'bg-success',
            'assigned': 'bg-primary',
            'reserved': 'bg-warning text-dark',
            'maintenance': 'bg-secondary',
            'blocked': 'bg-danger',
        }
        return status_classes.get(self.status, 'bg-secondary')
    
    def can_be_assigned_to(self, organe):
        """Vérifie si la fréquence peut être attribuée à un organe"""
        if not self.is_available():
            return False, "Fréquence non disponible"
        
        if not organe.has_coordinates():
            return False, "L'organe n'a pas de coordonnées géographiques"
        
        # Vérifier la distance minimale de réutilisation
        from django.db.models import Q
        from math import radians, cos, sin, asin, sqrt
        
        def haversine(lon1, lat1, lon2, lat2):
            """Calcule la distance entre deux points géographiques"""
            lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
            dlon = lon2 - lon1
            dlat = lat2 - lat1
            a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
            c = 2 * asin(sqrt(a))
            r = 6371  # Rayon de la Terre en kilomètres
            return c * r
        
        # Vérifier les autres attributions de la même fréquence
        other_assignments = Frequency.objects.filter(
            frequency_value=self.frequency_value,
            status='assigned'
        ).exclude(id=self.id)
        
        for other_freq in other_assignments:
            if other_freq.assigned_to and other_freq.assigned_to.has_coordinates():
                distance = haversine(
                    organe.longitude, organe.latitude,
                    other_freq.assigned_to.longitude, other_freq.assigned_to.latitude
                )
                if distance < self.min_distance_reuse:
                    return False, f"Distance insuffisante ({distance:.1f}km < {self.min_distance_reuse}km)"
        
        return True, "Attribution possible"
