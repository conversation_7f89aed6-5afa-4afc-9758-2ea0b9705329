@echo off
echo ========================================
echo Installation du Gestionnaire de Frequences
echo ========================================
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installé ou n'est pas dans le PATH
    echo Veuillez installer Python 3.8+ depuis https://python.org
    pause
    exit /b 1
)

echo Python detecte...
python --version

REM Créer un environnement virtuel
echo.
echo Creation de l'environnement virtuel...
python -m venv venv
if errorlevel 1 (
    echo ERREUR: Impossible de creer l'environnement virtuel
    echo Essayez: pip install virtualenv
    pause
    exit /b 1
)

REM Activer l'environnement virtuel
echo Activation de l'environnement virtuel...
call venv\Scripts\activate.bat

REM Mettre à jour pip
echo Mise a jour de pip...
python -m pip install --upgrade pip

REM Installer les dépendances
echo Installation des dependances...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERREUR: Impossible d'installer les dependances
    pause
    exit /b 1
)

REM Créer le répertoire logs
if not exist "logs" mkdir logs

REM Effectuer les migrations
echo.
echo Creation de la base de donnees...
python manage.py makemigrations
python manage.py migrate

REM Créer un superutilisateur
echo.
echo Creation du compte administrateur...
echo Veuillez entrer les informations pour le compte administrateur:
python manage.py createsuperuser

REM Collecter les fichiers statiques
echo.
echo Collection des fichiers statiques...
python manage.py collectstatic --noinput

echo.
echo ========================================
echo Installation terminee avec succes!
echo ========================================
echo.
echo Pour demarrer l'application:
echo 1. Activez l'environnement virtuel: venv\Scripts\activate.bat
echo 2. Lancez le serveur: python manage.py runserver
echo 3. Ouvrez votre navigateur sur: http://127.0.0.1:8000
echo.
pause
