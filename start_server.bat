@echo off
echo ========================================
echo Demarrage du Gestionnaire de Frequences
echo ========================================
echo.

REM Vérifier si l'environnement virtuel existe
if not exist "venv\Scripts\activate.bat" (
    echo ERREUR: Environnement virtuel non trouve
    echo Veuillez d'abord executer install.bat
    pause
    exit /b 1
)

REM Activer l'environnement virtuel
echo Activation de l'environnement virtuel...
call venv\Scripts\activate.bat

REM Vérifier si la base de données existe
if not exist "db.sqlite3" (
    echo Creation de la base de donnees...
    python manage.py migrate
)

REM Démarrer le serveur
echo.
echo Demarrage du serveur de developpement...
echo.
echo L'application sera accessible sur: http://127.0.0.1:8000
echo Pour arreter le serveur, appuyez sur Ctrl+C
echo.
python manage.py runserver 127.0.0.1:8000
