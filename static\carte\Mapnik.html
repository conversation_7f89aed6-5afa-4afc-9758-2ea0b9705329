b'<!DOCTYPE html>\n<html>\n<head>\n  <title>Mapnik - LeafLet Preview</title>\n  <meta charset="utf-8" />\n\n  <meta name="viewport" content="width=device-width, initial-scale=1.0">\n\n  <link rel="stylesheet" href="css/leaflet.css" />\n  <link href="css/jquery-ui.min.css" rel="stylesheet" type="text/css"/>\n  <style type="text/css">\n    #basemapslidercontainer {\n      position: absolute;\n      top: 50px;\n      right: 10px;\n      z-index: 1000;\n    }\n\n    #basemapslider{\n      font-size:62.5%;\n      margin: 14px;\n      height: 125px;\n      width:7px;\n    }\n\n    #map{\n       left: 10px;\n       right: 10px;\n       top: 10px;\n       bottom: 10px;\n    }\n  </style>\n  <script src="js/jquery.min.js"></script>\n  <script src="js/jquery-ui.min.js"></script>\n  <script src="js/leaflet.js"></script>\n  <script>\n    $(document).ready(function () {\n      $("#basemapslider").slider({\n            animate: true,\n            value: 1,\n            orientation: "vertical",\n            min: 0,\n            max: 1,\n            step: 0.1,\n            slide: function (event, ui) {\n                mytile.setOpacity(ui.value);\n            }\n       });\n\n        $(\'#basemapslider\').mousedown(function(){\n          map.dragging.disable();\n        })\n\n        $(\'#basemapslider\').mouseup(function(){\n          map.dragging.enable();\n        })\n\n      var map = L.map(\'map\').setView([28.72622550000181, -8.280948294019783], 7.0);\n      var baselayer = L.tileLayer(\'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\', {\n        maxZoom: 18,\n        attribution: \'Map data &copy; <a href="https://openstreetmap.org/copyright">OpenStreetMap</a> contributors\'\n      }).addTo(map);\n\n      var mytile =L.tileLayer(\'file:///C:/Users/<USER>/Desktop/testmap/carte/Mapnik/{z}/{x}/{y}.png\', {\n        maxZoom: 11,\n        tms: false,\n        attribution: \'Generated by QTiles\'\n      }).addTo(map);\n\n      L.control.layers({\'Basemap\':baselayer},{\'Mapnik\':mytile}).addTo(map);\n    })\n  </script>\n</head>\n<body>\n  <div id="map">\n    <div id="basemapslidercontainer">\n      <div id="basemapslider">\n    </div>\n  </div>\n  </div>\n</body>\n</html>\n'