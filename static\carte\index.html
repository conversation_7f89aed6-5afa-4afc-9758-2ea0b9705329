<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="utf-8" />
  <title>Carte Offline du Maroc</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="stylesheet" href="./leaflet/leaflet.css" />


  <style>
    html, body, #map { height: 100%; margin: 0; padding: 0; }
  </style>
</head>
<body>
  <div id="map"></div>

  <script src="./leaflet/leaflet.js"></script>
  <script>
    const map = L.map('map', {
      center: [31.5, -7.5],   // Centré sur le Maroc
      zoom: 5,
      minZoom: 3,
      maxZoom: 11
    });

    <PERSON><PERSON>tileLayer('./Mapnik/{z}/{x}/{y}.png', {
      attribution: 'Carte Offline QGIS',
      minZoom: 3,
      maxZoom: 11,
      tms: false, // ou true si tuiles inversées verticalement (tester si affichage inversé)
      errorTileUrl: '', // évite l'affichage d'icônes d'erreurs
    }).addTo(map);

    // Limite optionnelle de navigation
    const bounds = L.latLngBounds(
      [20.0, -25.0], // sud-ouest
      [36.5, 6.0]    // nord-est
    );
    map.setMaxBounds(bounds.pad(0.2));
  </script>
</body>
</html>
