{% extends 'base.html' %}
{% load static %}

{% block title %}Tableau de bord - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1>Tableau de bord - Test</h1>
    
    <div class="row">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5>Total Fréquences</h5>
                    <h2>{{ stats.total_frequencies|default:0 }}</h2>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5>Fréquences Attribuées</h5>
                    <h2>{{ stats.assigned_frequencies|default:0 }}</h2>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5>Fréquences Disponibles</h5>
                    <h2>{{ stats.available_frequencies|default:0 }}</h2>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <h5>Total Demandes</h5>
                    <h2>{{ stats.total_requests|default:0 }}</h2>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Informations Utilisateur</h5>
                </div>
                <div class="card-body">
                    <p><strong>Utilisateur :</strong> {{ user.get_full_name|default:user.username }}</p>
                    <p><strong>Rôle :</strong> {{ user.get_role_display }}</p>
                    <p><strong>Dernière connexion :</strong> {{ user.last_login|date:"d/m/Y H:i"|default:"Jamais" }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Actions Rapides</h5>
                </div>
                <div class="card-body">
                    <a href="{% url 'frequencies:list' %}" class="btn btn-primary me-2">
                        <i class="fas fa-broadcast-tower me-1"></i>Voir Fréquences
                    </a>
                    <a href="{% url 'requests:list' %}" class="btn btn-success me-2">
                        <i class="fas fa-file-alt me-1"></i>Voir Demandes
                    </a>
                    <a href="{% url 'gis_module:map' %}" class="btn btn-info me-2">
                        <i class="fas fa-map me-1"></i>Cartographie
                    </a>
                    <a href="{% url 'reports:dashboard' %}" class="btn btn-warning">
                        <i class="fas fa-chart-bar me-1"></i>Rapports
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
