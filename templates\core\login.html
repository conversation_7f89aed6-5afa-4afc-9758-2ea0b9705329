<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Gestionnaire de Fréquences FAR</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .far-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-card">
                    <div class="login-header">
                        <div class="far-logo">
                            <i class="fas fa-broadcast-tower"></i>
                        </div>
                        <h3 class="mb-0">Forces Armées Royales</h3>
                        <p class="mb-0">Gestionnaire de Fréquences</p>
                    </div>
                    
                    <div class="login-body">
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                        
                        <form method="post">
                            {% csrf_token %}
                            {% load crispy_forms_tags %}
                            
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    <i class="fas fa-user me-2"></i>{{ form.username.label }}
                                </label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.username.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.password.id_for_label }}" class="form-label">
                                    <i class="fas fa-lock me-2"></i>{{ form.password.label }}
                                </label>
                                {{ form.password }}
                                {% if form.password.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.password.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3 form-check">
                                {{ form.remember_me }}
                                <label class="form-check-label" for="{{ form.remember_me.id_for_label }}">
                                    {{ form.remember_me.label }}
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-login w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                            </button>
                        </form>
                        
                        <div class="text-center mt-3">
                            <a href="{% url 'auth:password_reset' %}" class="text-decoration-none">
                                <small>Mot de passe oublié ?</small>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <small class="text-white">
                        &copy; 2024 Forces Armées Royales - Système sécurisé
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
