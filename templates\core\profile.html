{% extends 'base.html' %}
{% load static %}

{% block title %}Mon Profil - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-user-edit me-2 text-primary"></i>
                Mon Profil
            </h1>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informations Personnelles</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">Prénom</label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                        <div class="text-danger">{{ form.first_name.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">Nom</label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                        <div class="text-danger">{{ form.last_name.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger">{{ form.email.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.telephone.id_for_label }}" class="form-label">Téléphone</label>
                                    {{ form.telephone }}
                                    {% if form.telephone.errors %}
                                        <div class="text-danger">{{ form.telephone.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.grade.id_for_label }}" class="form-label">Grade</label>
                                    {{ form.grade }}
                                    {% if form.grade.errors %}
                                        <div class="text-danger">{{ form.grade.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.unite.id_for_label }}" class="form-label">Unité</label>
                                    {{ form.unite }}
                                    {% if form.unite.errors %}
                                        <div class="text-danger">{{ form.unite.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'core:dashboard' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Retour au Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Mettre à jour
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Informations du Compte</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Nom d'utilisateur</h6>
                        <p class="text-muted">{{ user.username }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Matricule</h6>
                        <p class="text-muted">{{ user.matricule|default:"Non spécifié" }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Rôle</h6>
                        <span class="badge bg-primary">{{ user.get_role_display }}</span>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Membre depuis</h6>
                        <p class="text-muted">{{ user.date_joined|date:"d/m/Y" }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Dernière connexion</h6>
                        <p class="text-muted">{{ user.last_login|date:"d/m/Y H:i"|default:"Jamais" }}</p>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Activités Récentes</h6>
                </div>
                <div class="card-body">
                    {% if user_activities %}
                        <div class="list-group list-group-flush">
                            {% for activity in user_activities|slice:":5" %}
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ activity.get_action_display }}</h6>
                                        <p class="mb-1 small">{{ activity.description }}</p>
                                        <small class="text-muted">{{ activity.timestamp|date:"d/m/Y H:i" }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if user_activities.count > 5 %}
                        <div class="text-center mt-3">
                            <small class="text-muted">Et {{ user_activities.count|add:"-5" }} autres activités...</small>
                        </div>
                        {% endif %}
                    {% else %}
                        <p class="text-muted">Aucune activité récente.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
