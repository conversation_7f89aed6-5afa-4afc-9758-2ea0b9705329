{% extends 'base.html' %}
{% load static %}

{% block title %}<PERSON><PERSON><PERSON> un Utilisateur - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-user-plus me-2"></i>
                    Créer un Nouvel Utilisateur
                </h2>
                <a href="{% url 'core:user_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Retour à la liste
                </a>
            </div>

            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Informations de l'Utilisateur</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                                Nom d'utilisateur <span class="text-danger">*</span>
                                            </label>
                                            {{ form.username }}
                                            {% if form.username.errors %}
                                                <div class="text-danger">{{ form.username.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                                Email <span class="text-danger">*</span>
                                            </label>
                                            {{ form.email }}
                                            {% if form.email.errors %}
                                                <div class="text-danger">{{ form.email.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                                Prénom <span class="text-danger">*</span>
                                            </label>
                                            {{ form.first_name }}
                                            {% if form.first_name.errors %}
                                                <div class="text-danger">{{ form.first_name.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                                Nom <span class="text-danger">*</span>
                                            </label>
                                            {{ form.last_name }}
                                            {% if form.last_name.errors %}
                                                <div class="text-danger">{{ form.last_name.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.role.id_for_label }}" class="form-label">
                                                Rôle <span class="text-danger">*</span>
                                            </label>
                                            {{ form.role }}
                                            {% if form.role.errors %}
                                                <div class="text-danger">{{ form.role.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.grade.id_for_label }}" class="form-label">
                                                Grade
                                            </label>
                                            {{ form.grade }}
                                            {% if form.grade.errors %}
                                                <div class="text-danger">{{ form.grade.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.unite.id_for_label }}" class="form-label">
                                                Unité
                                            </label>
                                            {{ form.unite }}
                                            {% if form.unite.errors %}
                                                <div class="text-danger">{{ form.unite.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.telephone.id_for_label }}" class="form-label">
                                                Téléphone
                                            </label>
                                            {{ form.telephone }}
                                            {% if form.telephone.errors %}
                                                <div class="text-danger">{{ form.telephone.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.password1.id_for_label }}" class="form-label">
                                                Mot de passe <span class="text-danger">*</span>
                                            </label>
                                            {{ form.password1 }}
                                            {% if form.password1.errors %}
                                                <div class="text-danger">{{ form.password1.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.password2.id_for_label }}" class="form-label">
                                                Confirmer le mot de passe <span class="text-danger">*</span>
                                            </label>
                                            {{ form.password2 }}
                                            {% if form.password2.errors %}
                                                <div class="text-danger">{{ form.password2.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.is_active }}
                                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                            Compte actif
                                        </label>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'core:user_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        Annuler
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        Créer l'Utilisateur
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation côté client
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const password1 = document.getElementById('id_password1').value;
            const password2 = document.getElementById('id_password2').value;
            
            if (password1 !== password2) {
                e.preventDefault();
                alert('Les mots de passe ne correspondent pas.');
                return false;
            }
            
            if (password1.length < 8) {
                e.preventDefault();
                alert('Le mot de passe doit contenir au moins 8 caractères.');
                return false;
            }
        });
    }
});
</script>
{% endblock %}
