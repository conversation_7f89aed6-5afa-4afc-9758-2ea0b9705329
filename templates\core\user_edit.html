{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{% url 'core:user_list' %}">Utilisateurs</a></li>
            <li class="breadcrumb-item active">{{ title }}</li>
        </ol>
    </nav>

    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-edit me-2 text-primary"></i>
                        {{ title }}
                    </h1>
                    <p class="text-muted mb-0">Modification des informations utilisateur</p>
                </div>
                <div>
                    <a href="{% url 'core:user_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>Informations Utilisateur
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Nom d'utilisateur *</label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="{{ user_to_edit.username }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="matricule" class="form-label">Matricule *</label>
                                    <input type="text" class="form-control" id="matricule" name="matricule" 
                                           value="{{ user_to_edit.matricule }}" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">Prénom *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" 
                                           value="{{ user_to_edit.first_name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Nom *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" 
                                           value="{{ user_to_edit.last_name }}" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ user_to_edit.email }}" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">Rôle *</label>
                                    <select class="form-select" id="role" name="role" required>
                                        <option value="">Sélectionner un rôle</option>
                                        <option value="odr" {% if user_to_edit.role == 'odr' %}selected{% endif %}>ODR (Officier de Rang)</option>
                                        <option value="chef_section" {% if user_to_edit.role == 'chef_section' %}selected{% endif %}>Chef de Section</option>
                                        <option value="chef_division" {% if user_to_edit.role == 'chef_division' %}selected{% endif %}>Chef de Division</option>
                                        <option value="admin" {% if user_to_edit.role == 'admin' %}selected{% endif %}>Administrateur</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="grade" class="form-label">Grade</label>
                                    <input type="text" class="form-control" id="grade" name="grade" 
                                           value="{{ user_to_edit.grade|default:'' }}" placeholder="Ex: Lieutenant, Capitaine...">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="unite" class="form-label">Unité</label>
                                    <input type="text" class="form-control" id="unite" name="unite" 
                                           value="{{ user_to_edit.unite|default:'' }}" placeholder="Ex: 1er Régiment d'Infanterie">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="telephone" class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="telephone" name="telephone" 
                                           value="{{ user_to_edit.telephone|default:'' }}" placeholder="+212 xxx xxx xxx">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {% if user_to_edit.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    Compte actif
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'core:user_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Informations supplémentaires -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>Créé le :</strong> {{ user_to_edit.date_joined|date:"d/m/Y H:i" }}</p>
                    <p><strong>Dernière connexion :</strong> 
                        {% if user_to_edit.last_login %}
                            {{ user_to_edit.last_login|date:"d/m/Y H:i" }}
                        {% else %}
                            Jamais
                        {% endif %}
                    </p>
                    <p><strong>Statut actuel :</strong> 
                        {% if user_to_edit.is_active %}
                            <span class="badge bg-success">Actif</span>
                        {% else %}
                            <span class="badge bg-danger">Inactif</span>
                        {% endif %}
                    </p>
                    <p><strong>Rôle actuel :</strong> 
                        <span class="badge {{ user_to_edit.get_role_display_badge }}">
                            {{ user_to_edit.get_role_display }}
                        </span>
                    </p>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-key me-2"></i>Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-warning" onclick="resetPassword({{ user_to_edit.id }})">
                            <i class="fas fa-key me-2"></i>Réinitialiser mot de passe
                        </button>
                        {% if user_to_edit.is_active %}
                        <button class="btn btn-danger" onclick="toggleUserStatus({{ user_to_edit.id }}, false)">
                            <i class="fas fa-user-slash me-2"></i>Désactiver le compte
                        </button>
                        {% else %}
                        <button class="btn btn-success" onclick="toggleUserStatus({{ user_to_edit.id }}, true)">
                            <i class="fas fa-user-check me-2"></i>Activer le compte
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function resetPassword(userId) {
    if (confirm('Êtes-vous sûr de vouloir réinitialiser le mot de passe de cet utilisateur ?')) {
        fetch(`/users/${userId}/reset-password/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Nouveau mot de passe: ${data.new_password}`);
            } else {
                alert('Erreur lors de la réinitialisation du mot de passe');
            }
        });
    }
}

function toggleUserStatus(userId, activate) {
    const action = activate ? 'activer' : 'désactiver';
    if (confirm(`Êtes-vous sûr de vouloir ${action} cet utilisateur ?`)) {
        fetch(`/users/${userId}/toggle-status/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la modification du statut');
            }
        });
    }
}
</script>
{% endblock %}
