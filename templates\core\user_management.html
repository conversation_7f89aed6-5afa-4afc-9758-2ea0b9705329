{% extends 'base.html' %}
{% load static %}

{% block title %}Gestion des Utilisateurs - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-users me-2 text-primary"></i>
                        Gestion des Utilisateurs
                    </h1>
                    <p class="text-muted mb-0">Administration des comptes utilisateurs</p>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-user-plus me-2"></i>Nouvel Utilisateur
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques des utilisateurs -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Utilisateurs</h6>
                            <h3 class="mb-0">{{ stats.total_users }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Utilisateurs Actifs</h6>
                            <h3 class="mb-0">{{ stats.active_users }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Administrateurs</h6>
                            <h3 class="mb-0">{{ stats.admin_users }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-shield fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">ODR</h6>
                            <h3 class="mb-0">{{ stats.odr_users }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-tie fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des utilisateurs -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Liste des Utilisateurs
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="usersTable">
                            <thead>
                                <tr>
                                    <th>Utilisateur</th>
                                    <th>Matricule</th>
                                    <th>Grade</th>
                                    <th>Unité</th>
                                    <th>Rôle</th>
                                    <th>Statut</th>
                                    <th>Dernière Connexion</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                {{ user.first_name.0|default:user.username.0|upper }}
                                            </div>
                                            <div>
                                                <strong>{{ user.get_full_name }}</strong>
                                                <br><small class="text-muted">{{ user.email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ user.matricule }}</td>
                                    <td>{{ user.grade|default:"-" }}</td>
                                    <td>{{ user.unite|default:"-" }}</td>
                                    <td>
                                        <span class="badge {{ user.get_role_display_badge }}">
                                            {{ user.get_role_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if user.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                        {% else %}
                                        <span class="badge bg-danger">Inactif</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.last_login %}
                                        {{ user.last_login|date:"d/m/Y H:i" }}
                                        {% else %}
                                        <span class="text-muted">Jamais</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-primary" onclick="editUser({{ user.id }})" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="resetPassword({{ user.id }})" title="Réinitialiser mot de passe">
                                                <i class="fas fa-key"></i>
                                            </button>
                                            {% if user.is_active %}
                                            <button class="btn btn-outline-danger" onclick="toggleUserStatus({{ user.id }}, false)" title="Désactiver">
                                                <i class="fas fa-user-slash"></i>
                                            </button>
                                            {% else %}
                                            <button class="btn btn-outline-success" onclick="toggleUserStatus({{ user.id }}, true)" title="Activer">
                                                <i class="fas fa-user-check"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Ajouter Utilisateur -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>Nouvel Utilisateur
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm" method="post" action="{% url 'core:user_create' %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Nom d'utilisateur *</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="matricule" class="form-label">Matricule *</label>
                                <input type="text" class="form-control" id="matricule" name="matricule" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">Rôle *</label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Sélectionner un rôle</option>
                                    <option value="odr_gestionnaire">ODR Gestionnaire</option>
                                    <option value="chef_section">Chef de Section</option>
                                    <option value="chef_division">Chef de Division</option>
                                    <option value="admin">Administrateur</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="grade" class="form-label">Grade</label>
                                <input type="text" class="form-control" id="grade" name="grade" placeholder="Ex: Lieutenant, Capitaine...">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="unite" class="form-label">Unité</label>
                                <input type="text" class="form-control" id="unite" name="unite" placeholder="Ex: 1er Régiment d'Infanterie">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="telephone" class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" id="telephone" name="telephone" placeholder="+212 xxx xxx xxx">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password1" class="form-label">Mot de passe *</label>
                                <input type="password" class="form-control" id="password1" name="password1" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password2" class="form-label">Confirmer mot de passe *</label>
                                <input type="password" class="form-control" id="password2" name="password2" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Créer l'utilisateur
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 16px;
    font-weight: bold;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#usersTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json"
        },
        "pageLength": 25,
        "order": [[ 0, "asc" ]],
        "columnDefs": [
            { "orderable": false, "targets": -1 }
        ]
    });
});

function editUser(userId) {
    window.location.href = `/core/users/${userId}/edit/`;
}

function resetPassword(userId) {
    if (confirm('Êtes-vous sûr de vouloir réinitialiser le mot de passe de cet utilisateur ?')) {
        fetch(`/core/users/${userId}/reset-password/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Nouveau mot de passe: ${data.new_password}`);
            } else {
                alert('Erreur lors de la réinitialisation du mot de passe');
            }
        });
    }
}

function toggleUserStatus(userId, activate) {
    const action = activate ? 'activer' : 'désactiver';
    if (confirm(`Êtes-vous sûr de vouloir ${action} cet utilisateur ?`)) {
        fetch(`/core/users/${userId}/toggle-status/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la modification du statut');
            }
        });
    }
}
</script>
{% endblock %}
