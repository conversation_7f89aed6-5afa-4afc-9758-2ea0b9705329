{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-trash me-2 text-danger"></i>
                {{ title }}
            </h1>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Confirmation de Suppression
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-warning me-2"></i>
                        <strong>Attention !</strong> Cette action est irréversible. La fréquence sera définitivement supprimée de la base de données.
                    </div>

                    <h6>Fréquence à supprimer :</h6>
                    <div class="bg-light p-3 rounded mb-4">
                        <p><strong>Référence :</strong> {{ frequency.reference }}</p>
                        <p><strong>Fréquence :</strong> {{ frequency.frequency_value }} MHz</p>
                        <p><strong>Bande :</strong> {{ frequency.band.name }}</p>
                        <p><strong>Statut :</strong> 
                            <span class="badge {{ frequency.get_status_badge_class }}">
                                {{ frequency.get_status_display }}
                            </span>
                        </p>
                        {% if frequency.assigned_to_text or frequency.assigned_to %}
                        <p><strong>Attribuée à :</strong> 
                            {% if frequency.assigned_to_text %}
                                {{ frequency.assigned_to_text }}
                            {% elif frequency.assigned_to %}
                                {{ frequency.assigned_to.name }}
                            {% endif %}
                        </p>
                        {% endif %}
                        {% if frequency.assignment_date %}
                        <p><strong>Date d'attribution :</strong> {{ frequency.assignment_date|date:"d/m/Y H:i" }}</p>
                        {% endif %}
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'frequencies:detail' frequency.id %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>Supprimer Définitivement
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
