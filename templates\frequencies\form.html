{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{% url 'frequencies:list' %}">Fréquences</a></li>
            <li class="breadcrumb-item active">{{ title }}</li>
        </ol>
    </nav>

    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-broadcast-tower me-2 text-primary"></i>
                        {{ title }}
                    </h1>
                    <p class="text-muted mb-0">Saisie des informations de la fréquence</p>
                </div>
                <div>
                    <a href="{% url 'frequencies:list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>Formulaire de Fréquence
                    </h5>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                    <div class="alert alert-danger" role="alert">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Erreurs de validation :</h6>
                        <ul class="mb-0">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                <li>{{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        {% crispy form %}
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Aide contextuelle -->
    <div class="row justify-content-center mt-4">
        <div class="col-lg-10">
            <div class="card bg-light">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Aide
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Bandes de fréquences :</h6>
                            <ul class="small">
                                <li><strong>HF :</strong> 3-30 MHz (Haute Fréquence)</li>
                                <li><strong>VHF :</strong> 30-300 MHz (Très Haute Fréquence)</li>
                                <li><strong>UHF :</strong> 300-3000 MHz (Ultra Haute Fréquence)</li>
                                <li><strong>SHF :</strong> 3-30 GHz (Super Haute Fréquence)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Statuts disponibles :</h6>
                            <ul class="small">
                                <li><strong>Disponible :</strong> Fréquence libre d'utilisation</li>
                                <li><strong>Attribuée :</strong> Fréquence assignée à un organe</li>
                                <li><strong>Réservée :</strong> Fréquence réservée pour usage futur</li>
                                <li><strong>Bloquée :</strong> Fréquence temporairement indisponible</li>
                            </ul>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Notes importantes :</h6>
                            <ul class="small">
                                <li>La valeur de la fréquence doit être comprise dans la plage de la bande sélectionnée</li>
                                <li>La référence doit être unique dans le système</li>
                                <li>Si le statut est "Attribuée", un organe et une date d'attribution sont obligatoires</li>
                                <li>La distance minimale de réutilisation est utilisée pour éviter les interférences</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Validation côté client
    $('#id_frequency_value, #id_band').on('change', function() {
        validateFrequencyRange();
    });
    
    // Gestion du statut et de l'attribution
    $('#id_status').on('change', function() {
        const status = $(this).val();
        const assignedToField = $('#id_assigned_to');
        const assignmentDateField = $('#id_assignment_date');
        
        if (status === 'assigned') {
            assignedToField.prop('required', true);
            assignmentDateField.prop('required', true);
            assignedToField.closest('.form-group').find('label').addClass('required');
            assignmentDateField.closest('.form-group').find('label').addClass('required');
        } else {
            assignedToField.prop('required', false);
            assignmentDateField.prop('required', false);
            assignedToField.closest('.form-group').find('label').removeClass('required');
            assignmentDateField.closest('.form-group').find('label').removeClass('required');
            
            if (status !== 'assigned') {
                assignedToField.val('');
                assignmentDateField.val('');
            }
        }
    });
    
    // Déclencher la validation initiale
    $('#id_status').trigger('change');
});

function validateFrequencyRange() {
    const frequencyValue = parseFloat($('#id_frequency_value').val());
    const bandSelect = $('#id_band');
    const selectedBand = bandSelect.find('option:selected');
    
    if (frequencyValue && selectedBand.length) {
        // Récupérer les limites de la bande (à implémenter avec des données dynamiques)
        const bandRanges = {
            'HF': [3, 30],
            'VHF': [30, 300],
            'UHF': [300, 3000],
            'SHF': [3000, 30000]
        };
        
        const bandName = selectedBand.text().split(' ')[0];
        const range = bandRanges[bandName];
        
        if (range && (frequencyValue < range[0] || frequencyValue > range[1])) {
            $('#id_frequency_value').addClass('is-invalid');
            if (!$('#frequency-error').length) {
                $('#id_frequency_value').after(
                    `<div id="frequency-error" class="invalid-feedback">
                        La fréquence doit être comprise entre ${range[0]} et ${range[1]} MHz pour la bande ${bandName}
                    </div>`
                );
            }
        } else {
            $('#id_frequency_value').removeClass('is-invalid');
            $('#frequency-error').remove();
        }
    }
}
</script>

<style>
.required::after {
    content: " *";
    color: red;
}
</style>
{% endblock %}
