{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{% url 'frequencies:list' %}">Fréquences</a></li>
            <li class="breadcrumb-item active">{{ title }}</li>
        </ol>
    </nav>

    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-file-excel me-2 text-success"></i>
                        {{ title }}
                    </h1>
                    <p class="text-muted mb-0">Import en masse de fréquences depuis un fichier Excel</p>
                </div>
                <div>
                    <a href="{% url 'frequencies:list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <!-- Formulaire d'import -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-upload me-2"></i>Télécharger un fichier Excel
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="importForm">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <label for="excel_file" class="form-label">
                                <i class="fas fa-file-excel me-2"></i>Fichier Excel (.xlsx ou .xls)
                            </label>
                            <input type="file" class="form-control" id="excel_file" name="excel_file" 
                                   accept=".xlsx,.xls" required>
                            <div class="form-text">
                                Sélectionnez un fichier Excel contenant les données des fréquences à importer.
                            </div>
                        </div>
                        
                        <div class="alert alert-info" role="alert">
                            <h6><i class="fas fa-info-circle me-2"></i>Avant d'importer :</h6>
                            <ul class="mb-0">
                                <li>Téléchargez le modèle Excel ci-dessous</li>
                                <li>Remplissez vos données en respectant le format</li>
                                <li>Vérifiez que les bandes et organes existent dans le système</li>
                                <li>Assurez-vous que les références sont uniques</li>
                            </ul>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'frequencies:export_template' %}" class="btn btn-outline-success">
                                <i class="fas fa-download me-2"></i>Télécharger le modèle Excel
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-upload me-2"></i>Importer les fréquences
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>Instructions
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Colonnes obligatoires :</h6>
                    <ul class="small">
                        <li><strong>frequency_value :</strong> Valeur en MHz</li>
                        <li><strong>band :</strong> HF, VHF, UHF, SHF</li>
                        <li><strong>reference :</strong> Référence unique</li>
                    </ul>
                    
                    <h6>Colonnes optionnelles :</h6>
                    <ul class="small">
                        <li><strong>status :</strong> available, assigned, reserved, blocked</li>
                        <li><strong>assigned_to :</strong> Code de l'organe</li>
                        <li><strong>nature :</strong> permanent, temporary</li>
                        <li><strong>network_type :</strong> command, tactical, logistics</li>
                        <li><strong>power_max :</strong> Puissance en Watts</li>
                        <li><strong>coverage_radius :</strong> Rayon en km</li>
                        <li><strong>latitude/longitude :</strong> Coordonnées GPS</li>
                        <li><strong>notes :</strong> Commentaires</li>
                    </ul>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Attention
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <small>
                            <strong>Important :</strong> L'import vérifiera l'unicité des références. 
                            Les fréquences avec des références existantes seront ignorées.
                        </small>
                    </div>
                    
                    <div class="alert alert-info" role="alert">
                        <small>
                            <strong>Conseil :</strong> Testez d'abord avec un petit fichier 
                            pour vérifier le format avant d'importer un gros volume.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Exemples de données -->
    <div class="row justify-content-center mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>Exemple de données
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>frequency_value</th>
                                    <th>band</th>
                                    <th>reference</th>
                                    <th>status</th>
                                    <th>assigned_to</th>
                                    <th>nature</th>
                                    <th>network_type</th>
                                    <th>power_max</th>
                                    <th>coverage_radius</th>
                                    <th>latitude</th>
                                    <th>longitude</th>
                                    <th>notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>145.500</td>
                                    <td>VHF</td>
                                    <td>VHF-001</td>
                                    <td>available</td>
                                    <td></td>
                                    <td>permanent</td>
                                    <td>command</td>
                                    <td>50</td>
                                    <td>25</td>
                                    <td>33.9716</td>
                                    <td>-6.8498</td>
                                    <td>Fréquence de commandement</td>
                                </tr>
                                <tr>
                                    <td>435.750</td>
                                    <td>UHF</td>
                                    <td>UHF-001</td>
                                    <td>assigned</td>
                                    <td>1RI</td>
                                    <td>permanent</td>
                                    <td>tactical</td>
                                    <td>25</td>
                                    <td>15</td>
                                    <td>33.5731</td>
                                    <td>-7.5898</td>
                                    <td>Attribuée au 1er RI</td>
                                </tr>
                                <tr>
                                    <td>28.500</td>
                                    <td>HF</td>
                                    <td>HF-001</td>
                                    <td>available</td>
                                    <td></td>
                                    <td>temporary</td>
                                    <td>logistics</td>
                                    <td>100</td>
                                    <td>100</td>
                                    <td>34.0209</td>
                                    <td>-6.8416</td>
                                    <td>Fréquence HF longue portée</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#importForm').on('submit', function(e) {
        const fileInput = $('#excel_file')[0];
        const submitBtn = $('#submitBtn');
        
        if (!fileInput.files.length) {
            e.preventDefault();
            alert('Veuillez sélectionner un fichier Excel.');
            return;
        }
        
        const file = fileInput.files[0];
        const maxSize = 10 * 1024 * 1024; // 10MB
        
        if (file.size > maxSize) {
            e.preventDefault();
            alert('Le fichier est trop volumineux. Taille maximale : 10MB.');
            return;
        }
        
        // Désactiver le bouton et afficher un indicateur de chargement
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Import en cours...');
        
        // Afficher une barre de progression (simulation)
        showProgressBar();
    });
    
    $('#excel_file').on('change', function() {
        const file = this.files[0];
        if (file) {
            const fileName = file.name;
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            
            // Afficher les informations du fichier
            const fileInfo = `
                <div class="alert alert-success mt-2" role="alert">
                    <i class="fas fa-file-excel me-2"></i>
                    <strong>Fichier sélectionné :</strong> ${fileName} (${fileSize} MB)
                </div>
            `;
            
            $('.form-text').after(fileInfo);
        }
    });
});

function showProgressBar() {
    const progressHtml = `
        <div class="progress mt-3" id="importProgress">
            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                 role="progressbar" style="width: 0%"></div>
        </div>
    `;
    
    $('#importForm').after(progressHtml);
    
    // Simulation de progression
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress > 90) progress = 90;
        
        $('.progress-bar').css('width', progress + '%');
        
        if (progress >= 90) {
            clearInterval(interval);
        }
    }, 500);
}
</script>
{% endblock %}
