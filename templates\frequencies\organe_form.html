{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{% url 'frequencies:organe_list' %}">Organes</a></li>
            <li class="breadcrumb-item active">{{ title }}</li>
        </ol>
    </nav>

    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-map-marker-alt me-2 text-success"></i>
                        {{ title }}
                    </h1>
                    <p class="text-muted mb-0">Saisie des informations de l'organe militaire</p>
                </div>
                <div>
                    <a href="{% url 'frequencies:organe_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>Formulaire d'Organe
                    </h5>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                    <div class="alert alert-danger" role="alert">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Erreurs de validation :</h6>
                        <ul class="mb-0">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                <li>{{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        {% crispy form %}
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Aide contextuelle -->
    <div class="row justify-content-center mt-4">
        <div class="col-lg-10">
            <div class="card bg-light">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Guide de Saisie
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Types d'organes :</h6>
                            <ul class="small">
                                <li><strong>Régiment :</strong> Unité de base de l'armée</li>
                                <li><strong>Bataillon :</strong> Subdivision d'un régiment</li>
                                <li><strong>Compagnie :</strong> Subdivision d'un bataillon</li>
                                <li><strong>Escadron :</strong> Unité de cavalerie ou blindés</li>
                                <li><strong>État-Major :</strong> Organe de commandement</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Coordonnées GPS :</h6>
                            <ul class="small">
                                <li><strong>Latitude :</strong> Position Nord-Sud (ex: 33.9716)</li>
                                <li><strong>Longitude :</strong> Position Est-Ouest (ex: -6.8498)</li>
                                <li>Utilisez des coordonnées décimales</li>
                                <li>Précision recommandée : 6 décimales</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Conseils de saisie :</h6>
                            <ul class="small">
                                <li>Le code organe doit être unique et court (ex: 1RI, 2BP, EMG)</li>
                                <li>Indiquez le commandant avec son grade complet</li>
                                <li>L'adresse complète facilite la localisation</li>
                                <li>Les coordonnées GPS permettent l'affichage sur la carte</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3" role="alert">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Astuce :</strong> Vous pouvez utiliser Google Maps pour obtenir les coordonnées GPS précises 
                        en cliquant droit sur la localisation et en sélectionnant les coordonnées.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Validation des coordonnées GPS
    $('#id_latitude, #id_longitude').on('change', function() {
        validateCoordinates();
    });
    
    // Auto-génération du code organe basé sur le nom
    $('#id_name').on('blur', function() {
        const name = $(this).val();
        const codeField = $('#id_code');
        
        if (name && !codeField.val()) {
            // Extraire les initiales pour générer un code
            const words = name.split(' ');
            let code = '';
            
            words.forEach(word => {
                if (word.length > 0 && !['de', 'du', 'des', 'le', 'la', 'les', 'd\''].includes(word.toLowerCase())) {
                    code += word.charAt(0).toUpperCase();
                }
            });
            
            // Limiter à 5 caractères maximum
            if (code.length > 5) {
                code = code.substring(0, 5);
            }
            
            codeField.val(code);
        }
    });
});

function validateCoordinates() {
    const latitude = parseFloat($('#id_latitude').val());
    const longitude = parseFloat($('#id_longitude').val());
    
    // Validation de la latitude (Maroc: environ 21° à 36° Nord)
    if (latitude && (latitude < 20 || latitude > 37)) {
        showFieldError('id_latitude', 'La latitude doit être comprise entre 20° et 37° pour le Maroc');
    } else {
        clearFieldError('id_latitude');
    }
    
    // Validation de la longitude (Maroc: environ -17° à -1° Ouest)
    if (longitude && (longitude < -18 || longitude > 0)) {
        showFieldError('id_longitude', 'La longitude doit être comprise entre -18° et 0° pour le Maroc');
    } else {
        clearFieldError('id_longitude');
    }
}

function showFieldError(fieldId, message) {
    const field = $('#' + fieldId);
    field.addClass('is-invalid');
    
    // Supprimer l'ancien message d'erreur
    field.siblings('.invalid-feedback').remove();
    
    // Ajouter le nouveau message
    field.after(`<div class="invalid-feedback">${message}</div>`);
}

function clearFieldError(fieldId) {
    const field = $('#' + fieldId);
    field.removeClass('is-invalid');
    field.siblings('.invalid-feedback').remove();
}
</script>
{% endblock %}
