{% extends 'base.html' %}
{% load static %}

{% block title %}Organes Militaires - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-map-marker-alt me-2 text-success"></i>
                        Organes Militaires
                    </h1>
                    <p class="text-muted mb-0">Gestion des unités et organes militaires</p>
                </div>
                <div>
                    {% if user.can_validate_technical %}
                    <a href="{% url 'frequencies:organe_create' %}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Nouvel Organe
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Organes</h6>
                            <h3 class="mb-0">{{ stats.total_organes }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-map-marker-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Organes Actifs</h6>
                            <h3 class="mb-0">{{ stats.active_organes }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Avec Fréquences</h6>
                            <h3 class="mb-0">{{ stats.with_frequencies }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-broadcast-tower fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des organes -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Liste des Organes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="organesTable">
                            <thead>
                                <tr>
                                    <th>Organe</th>
                                    <th>Code</th>
                                    <th>Type</th>
                                    <th>Commandant</th>
                                    <th>Localisation</th>
                                    <th>Fréquences</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for organe in organes %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                {{ organe.code.0|upper }}
                                            </div>
                                            <div>
                                                <strong>{{ organe.name }}</strong>
                                                {% if organe.parent_organe %}
                                                <br><small class="text-muted">{{ organe.parent_organe.name }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ organe.code }}</span>
                                    </td>
                                    <td>
                                        <span class="badge {{ organe.get_type_badge_class }}">
                                            {{ organe.get_type_organe_display }}
                                        </span>
                                    </td>
                                    <td>{{ organe.commander|default:"-" }}</td>
                                    <td>
                                        {% if organe.latitude and organe.longitude %}
                                        <small>
                                            <i class="fas fa-map-pin me-1"></i>
                                            {{ organe.latitude|floatformat:4 }}, {{ organe.longitude|floatformat:4 }}
                                        </small>
                                        {% else %}
                                        <span class="text-muted">Non définie</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ organe.assigned_frequencies.count }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if organe.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                        {% else %}
                                        <span class="badge bg-danger">Inactif</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{% url 'frequencies:organe_detail' organe.id %}" 
                                               class="btn btn-outline-primary" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if user.can_validate_technical %}
                                            <button class="btn btn-outline-warning" onclick="editOrgane({{ organe.id }})" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-map-marker-alt fa-3x mb-3 opacity-50"></i>
                                        <p>Aucun organe trouvé</p>
                                        {% if user.can_validate_technical %}
                                        <a href="{% url 'frequencies:organe_create' %}" class="btn btn-success">
                                            <i class="fas fa-plus me-2"></i>Créer le premier organe
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 16px;
    font-weight: bold;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#organesTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json"
        },
        "pageLength": 25,
        "order": [[ 0, "asc" ]],
        "columnDefs": [
            { "orderable": false, "targets": -1 }
        ]
    });
});

function editOrgane(organeId) {
    // TODO: Implémenter l'édition d'organe
    alert('Fonctionnalité d\'édition en cours de développement');
}
</script>
{% endblock %}
