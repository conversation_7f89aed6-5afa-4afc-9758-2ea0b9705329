{% extends 'base.html' %}
{% load static %}

{% block title %}Cartographie - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-map me-2 text-primary"></i>
                        Cartographie des Fréquences
                        <span class="badge bg-success ms-2">
                            <i class="fas fa-wifi-slash me-1"></i>
                            Mode Offline
                        </span>
                    </h1>
                    <p class="text-muted mb-0">Visualisation géographique des fréquences et organes</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary" onclick="toggleLayers()">
                        <i class="fas fa-layer-group me-2"></i>Couches
                    </button>
                    <button class="btn btn-outline-secondary" onclick="resetView()">
                        <i class="fas fa-home me-2"></i>Vue initiale
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Carte principale -->
        <div class="col-lg-9">
            <div class="card">
                <div class="card-body p-0">
                    <div id="mainMap" style="height: 600px;"></div>
                </div>
            </div>
        </div>

        <!-- Panneau de contrôle -->
        <div class="col-lg-3">
            <!-- Contrôles des couches -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-layer-group me-2"></i>Couches
                    </h6>
                </div>
                <div class="card-body">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="showFrequencies" checked>
                        <label class="form-check-label" for="showFrequencies">
                            <i class="fas fa-broadcast-tower me-2 text-primary"></i>Fréquences
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="showOrganes" checked>
                        <label class="form-check-label" for="showOrganes">
                            <i class="fas fa-map-marker-alt me-2 text-success"></i>Organes
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="showCoverage">
                        <label class="form-check-label" for="showCoverage">
                            <i class="fas fa-circle me-2 text-info"></i>Zones de couverture
                        </label>
                    </div>
                </div>
            </div>

            <!-- Filtres -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>Filtres
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="bandFilter" class="form-label">Bande de fréquence</label>
                        <select class="form-select" id="bandFilter" onchange="applyFilters()">
                            <option value="">Toutes les bandes</option>
                            <option value="HF">HF</option>
                            <option value="VHF">VHF</option>
                            <option value="UHF">UHF</option>
                            <option value="SHF">SHF</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="statusFilter" class="form-label">Statut</label>
                        <select class="form-select" id="statusFilter" onchange="applyFilters()">
                            <option value="">Tous les statuts</option>
                            <option value="available">Disponible</option>
                            <option value="assigned">Attribuée</option>
                            <option value="reserved">Réservée</option>
                            <option value="blocked">Bloquée</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="organeTypeFilter" class="form-label">Type d'organe</label>
                        <select class="form-select" id="organeTypeFilter" onchange="applyFilters()">
                            <option value="">Tous les types</option>
                            <option value="regiment">Régiment</option>
                            <option value="bataillon">Bataillon</option>
                            <option value="compagnie">Compagnie</option>
                            <option value="escadron">Escadron</option>
                            <option value="etat_major">État-Major</option>
                        </select>
                    </div>
                    <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                        <i class="fas fa-times me-2"></i>Effacer les filtres
                    </button>
                </div>
            </div>

            <!-- Légende -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Légende
                    </h6>
                </div>
                <div class="card-body">
                    <div class="legend-item mb-2">
                        <i class="fas fa-broadcast-tower text-primary me-2"></i>
                        <span>Fréquences attribuées</span>
                    </div>
                    <div class="legend-item mb-2">
                        <i class="fas fa-map-marker-alt text-success me-2"></i>
                        <span>Organes militaires</span>
                    </div>
                    <div class="legend-item mb-2">
                        <i class="fas fa-circle text-info me-2"></i>
                        <span>Zones de couverture</span>
                    </div>
                    
                    <hr>
                    
                    <h6>Bandes de fréquences:</h6>
                    <div class="legend-item mb-1">
                        <span class="badge freq-hf me-2">HF</span>
                        <small>3-30 MHz</small>
                    </div>
                    <div class="legend-item mb-1">
                        <span class="badge freq-vhf me-2">VHF</span>
                        <small>30-300 MHz</small>
                    </div>
                    <div class="legend-item mb-1">
                        <span class="badge freq-uhf me-2">UHF</span>
                        <small>300-3000 MHz</small>
                    </div>
                    <div class="legend-item mb-1">
                        <span class="badge freq-shf me-2">SHF</span>
                        <small>3-30 GHz</small>
                    </div>
                </div>
            </div>

            <!-- Statistiques -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistiques
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Fréquences visibles:</span>
                        <span id="frequencyCount">0</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Organes visibles:</span>
                        <span id="organeCount">0</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Zones de couverture:</span>
                        <span id="coverageCount">0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.legend-item {
    display: flex;
    align-items: center;
}

.leaflet-popup-content {
    margin: 8px 12px;
    line-height: 1.4;
}

.leaflet-popup-content h6 {
    margin-bottom: 5px;
    color: #333;
}

.leaflet-popup-content .badge {
    font-size: 0.7em;
}

/* Styles pour les marqueurs personnalisés */
.frequency-marker {
    background-color: #007bff;
    border: 2px solid #fff;
    border-radius: 50%;
    width: 12px;
    height: 12px;
}

.organe-marker {
    background-color: #28a745;
    border: 2px solid #fff;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Variables globales
let mainMap;
let frequencyLayer;
let organeLayer;
let coverageLayer;
let frequencyData = [];
let organeData = [];

// Initialisation de la carte
function initMap() {
    // Créer la carte
    mainMap = L.map('mainMap').setView({{ map_config.center }}, {{ map_config.zoom }});

    // Ajouter la couche de base - Carte offline du Maroc
    L.tileLayer('/gis/tiles/{z}/{x}/{y}.png', {
        attribution: '© Carte Offline Maroc - Forces Armées Royales',
        minZoom: 3,
        maxZoom: 11,
        bounds: [
            [21.0, -17.0],  // Sud-Ouest du Maroc
            [36.0, -1.0]    // Nord-Est du Maroc
        ]
    }).addTo(mainMap);

    // Créer les groupes de couches
    frequencyLayer = L.layerGroup().addTo(mainMap);
    organeLayer = L.layerGroup().addTo(mainMap);
    coverageLayer = L.layerGroup();

    // Charger les données
    loadFrequencies();
    loadOrganes();
}

// Charger les fréquences
function loadFrequencies() {
    fetch('{% url "gis_module:frequencies_geojson" %}')
        .then(response => response.json())
        .then(data => {
            frequencyData = data.features;
            displayFrequencies();
            updateStats();
        })
        .catch(error => {
            console.error('Erreur lors du chargement des fréquences:', error);
        });
}

// Charger les organes
function loadOrganes() {
    fetch('{% url "gis_module:organes_geojson" %}')
        .then(response => response.json())
        .then(data => {
            organeData = data.features;
            displayOrganes();
            updateStats();
        })
        .catch(error => {
            console.error('Erreur lors du chargement des organes:', error);
        });
}

// Afficher les fréquences
function displayFrequencies() {
    frequencyLayer.clearLayers();
    coverageLayer.clearLayers();

    frequencyData.forEach(feature => {
        if (shouldShowFeature(feature, 'frequency')) {
            const coords = feature.geometry.coordinates;
            const props = feature.properties;

            // Marqueur de fréquence
            const marker = L.circleMarker([coords[1], coords[0]], {
                radius: 8,
                fillColor: getBandColor(props.band),
                color: '#fff',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8
            });

            // Popup
            const popupContent = `
                <div class="frequency-popup">
                    <h6><i class="fas fa-broadcast-tower"></i> ${props.frequency} MHz</h6>
                    <p><strong>Référence:</strong> ${props.reference}</p>
                    <p><strong>Bande:</strong> <span class="badge freq-${props.band.toLowerCase()}">${props.band}</span></p>
                    <p><strong>Statut:</strong> <span class="badge ${getStatusBadgeClass(props.status)}">${getStatusLabel(props.status)}</span></p>
                    ${props.organe ? `<p><strong>Attribuée à:</strong> ${props.organe}</p>` : ''}
                    <div class="mt-2">
                        <a href="/frequencies/${props.id}/" class="btn btn-sm btn-primary">Voir détails</a>
                    </div>
                </div>
            `;

            marker.bindPopup(popupContent);
            frequencyLayer.addLayer(marker);

            // Zone de couverture (si activée)
            if (document.getElementById('showCoverage').checked && props.coverage_radius) {
                const circle = L.circle([coords[1], coords[0]], {
                    radius: props.coverage_radius * 1000, // Convertir km en mètres
                    color: getBandColor(props.band),
                    fillColor: getBandColor(props.band),
                    fillOpacity: 0.1,
                    weight: 1
                });
                coverageLayer.addLayer(circle);
            }
        }
    });

    // Ajouter la couche de couverture si activée
    if (document.getElementById('showCoverage').checked) {
        if (!mainMap.hasLayer(coverageLayer)) {
            mainMap.addLayer(coverageLayer);
        }
    } else {
        if (mainMap.hasLayer(coverageLayer)) {
            mainMap.removeLayer(coverageLayer);
        }
    }
}

// Afficher les organes
function displayOrganes() {
    organeLayer.clearLayers();

    organeData.forEach(feature => {
        if (shouldShowFeature(feature, 'organe')) {
            const coords = feature.geometry.coordinates;
            const props = feature.properties;

            // Marqueur d'organe
            const marker = L.circleMarker([coords[1], coords[0]], {
                radius: 6,
                fillColor: '#28a745',
                color: '#fff',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8
            });

            // Popup
            const popupContent = `
                <div class="organe-popup">
                    <h6><i class="fas fa-map-marker-alt"></i> ${props.name}</h6>
                    <p><strong>Code:</strong> ${props.code}</p>
                    <p><strong>Type:</strong> ${getOrganeTypeLabel(props.type)}</p>
                    ${props.commander ? `<p><strong>Commandant:</strong> ${props.commander}</p>` : ''}
                    ${props.assigned_frequencies_count ? `<p><strong>Fréquences:</strong> ${props.assigned_frequencies_count}</p>` : ''}
                    <div class="mt-2">
                        <a href="/frequencies/organes/${props.id}/" class="btn btn-sm btn-success">Voir détails</a>
                    </div>
                </div>
            `;

            marker.bindPopup(popupContent);
            organeLayer.addLayer(marker);
        }
    });
}

// Fonctions utilitaires
function getBandColor(band) {
    const colors = {
        'HF': '#e74c3c',
        'VHF': '#3498db',
        'UHF': '#9b59b6',
        'SHF': '#f39c12'
    };
    return colors[band] || '#6c757d';
}

function getStatusBadgeClass(status) {
    const classes = {
        'available': 'bg-success',
        'assigned': 'bg-primary',
        'reserved': 'bg-warning text-dark',
        'blocked': 'bg-danger'
    };
    return classes[status] || 'bg-secondary';
}

function getStatusLabel(status) {
    const labels = {
        'available': 'Disponible',
        'assigned': 'Attribuée',
        'reserved': 'Réservée',
        'blocked': 'Bloquée'
    };
    return labels[status] || status;
}

function getOrganeTypeLabel(type) {
    const labels = {
        'regiment': 'Régiment',
        'bataillon': 'Bataillon',
        'compagnie': 'Compagnie',
        'escadron': 'Escadron',
        'etat_major': 'État-Major'
    };
    return labels[type] || type;
}

function shouldShowFeature(feature, type) {
    const bandFilter = document.getElementById('bandFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const organeTypeFilter = document.getElementById('organeTypeFilter').value;

    if (type === 'frequency') {
        if (bandFilter && feature.properties.band !== bandFilter) return false;
        if (statusFilter && feature.properties.status !== statusFilter) return false;
    } else if (type === 'organe') {
        if (organeTypeFilter && feature.properties.type !== organeTypeFilter) return false;
    }

    return true;
}

// Contrôles
function toggleLayers() {
    const showFreq = document.getElementById('showFrequencies').checked;
    const showOrg = document.getElementById('showOrganes').checked;
    const showCov = document.getElementById('showCoverage').checked;

    if (showFreq) {
        if (!mainMap.hasLayer(frequencyLayer)) mainMap.addLayer(frequencyLayer);
    } else {
        if (mainMap.hasLayer(frequencyLayer)) mainMap.removeLayer(frequencyLayer);
    }

    if (showOrg) {
        if (!mainMap.hasLayer(organeLayer)) mainMap.addLayer(organeLayer);
    } else {
        if (mainMap.hasLayer(organeLayer)) mainMap.removeLayer(organeLayer);
    }

    displayFrequencies(); // Recharger pour les zones de couverture
    updateStats();
}

function applyFilters() {
    displayFrequencies();
    displayOrganes();
    updateStats();
}

function clearFilters() {
    document.getElementById('bandFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('organeTypeFilter').value = '';
    applyFilters();
}

function resetView() {
    mainMap.setView({{ map_config.center }}, {{ map_config.zoom }});
}

function updateStats() {
    const visibleFreq = frequencyLayer.getLayers().length;
    const visibleOrg = organeLayer.getLayers().length;
    const visibleCov = coverageLayer.getLayers().length;

    document.getElementById('frequencyCount').textContent = visibleFreq;
    document.getElementById('organeCount').textContent = visibleOrg;
    document.getElementById('coverageCount').textContent = visibleCov;
}

// Event listeners
document.getElementById('showFrequencies').addEventListener('change', toggleLayers);
document.getElementById('showOrganes').addEventListener('change', toggleLayers);
document.getElementById('showCoverage').addEventListener('change', toggleLayers);

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    initMap();
});
</script>
{% endblock %}
