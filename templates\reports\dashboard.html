{% extends 'base.html' %}
{% load static %}

{% block title %}Rapports et Statistiques - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                        Rapports et Statistiques
                    </h1>
                    <p class="text-muted mb-0">Analyse et génération de rapports</p>
                </div>
                <div>
                    <a href="{% url 'reports:generate' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Nouveau Rapport
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques générales -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Fréquences
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_frequencies|default:0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-broadcast-tower fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Fréquences Attribuées
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ assigned_frequencies|default:0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Demandes ce mois
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ pending_requests|default:0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Taux d'approbation
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ approval_rate|default:0|floatformat:1 }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Graphiques -->
        <div class="col-lg-8">
            <!-- Répartition par bande -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Répartition des Fréquences par Bande
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="bandChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Évolution des demandes -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>Évolution des Demandes (6 derniers mois)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="requestsChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Statut des demandes -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statut des Demandes
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Actions rapides -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">Actions Rapides</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'reports:generate' %}" class="btn btn-primary">
                            <i class="fas fa-file-pdf me-2"></i>Rapport Annuel
                        </a>
                        <a href="{% url 'reports:statistics' %}" class="btn btn-outline-info">
                            <i class="fas fa-chart-line me-2"></i>Statistiques Détaillées
                        </a>
                        <button class="btn btn-outline-success" onclick="exportData()">
                            <i class="fas fa-download me-2"></i>Exporter Données
                        </button>
                    </div>
                </div>
            </div>

            <!-- Rapports récents -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">Rapports Récents</h6>
                </div>
                <div class="card-body">
                    {% if recent_reports %}
                    <div class="list-group list-group-flush">
                        {% for report in recent_reports %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">{{ report.title }}</div>
                                <small class="text-muted">{{ report.generated_at|date:"d/m/Y" }}</small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'reports:view' report.id %}" class="btn btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'reports:download' report.id %}" class="btn btn-outline-success">
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center">Aucun rapport récent</p>
                    {% endif %}
                </div>
            </div>

            <!-- Alertes -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Alertes
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-clock me-2"></i>
                        <strong>{{ pending_requests|default:0 }}</strong> demandes en attente de traitement
                    </div>
                    
                    {% comment %} Fréquences expirées supprimées {% endcomment %}
                    
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        Taux d'utilisation: <strong>{{ usage_rate|default:0|floatformat:1 }}%</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Données réelles pour les graphiques
const bandData = {
    labels: [{% for band in band_stats %}'{{ band.name }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
    datasets: [{
        data: [{% for band in band_stats %}{{ band.total }}{% if not forloop.last %}, {% endif %}{% endfor %}],
        backgroundColor: ['#e74c3c', '#3498db', '#9b59b6', '#f39c12'],
        borderWidth: 2,
        borderColor: '#fff'
    }]
};

// Données mensuelles réelles (passées depuis la vue)
const monthlyData = {{ monthly_stats|safe }};
const requestsData = {
    labels: monthlyData.map(item => item.month),
    datasets: [{
        label: 'Demandes soumises',
        data: monthlyData.map(item => item.requests),
        borderColor: '#007bff',
        backgroundColor: 'rgba(0, 123, 255, 0.1)',
        tension: 0.4
    }]
};

const statusData = {
    labels: ['En attente', 'Approuvées', 'Disponibles'],
    datasets: [{
        data: [{{ pending_requests }}, {{ approved_requests }}, {{ available_frequencies }}],
        backgroundColor: ['#ffc107', '#28a745', '#17a2b8'],
        borderWidth: 2,
        borderColor: '#fff'
    }]
};

// Configuration des graphiques
const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom'
        }
    }
};

// Initialisation des graphiques
document.addEventListener('DOMContentLoaded', function() {
    // Graphique en secteurs pour les bandes
    const bandCtx = document.getElementById('bandChart').getContext('2d');
    new Chart(bandCtx, {
        type: 'doughnut',
        data: bandData,
        options: {
            ...chartOptions,
            cutout: '60%'
        }
    });

    // Graphique linéaire pour l'évolution des demandes
    const requestsCtx = document.getElementById('requestsChart').getContext('2d');
    new Chart(requestsCtx, {
        type: 'line',
        data: requestsData,
        options: {
            ...chartOptions,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Graphique en barres pour le statut des demandes
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'bar',
        data: statusData,
        options: {
            ...chartOptions,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});

function exportData() {
    // TODO: Implémenter l'export des données
    alert('Fonctionnalité d\'export en cours de développement');
}
</script>
{% endblock %}
