{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-link me-2 text-primary"></i>
                {{ title }}
            </h1>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Attribution de Fréquence</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ODR Gestionnaire :</strong> Vous êtes sur le point d'attribuer une fréquence à cette demande.
                        Cette action finalisera le processus d'attribution.
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <h6>R<PERSON><PERSON><PERSON> de la demande :</h6>
                            <div class="bg-light p-3 rounded">
                                <p><strong>Référence :</strong> {{ freq_request.reference }}</p>
                                <p><strong>Demandeur :</strong> {{ freq_request.requesting_organe_text }}</p>
                                <p><strong>Type :</strong> {{ freq_request.get_request_type_display }}</p>
                                <p><strong>Bande :</strong> {{ freq_request.frequency_band.name }}</p>
                                <p><strong>Fréquence préférée :</strong> 
                                    {% if freq_request.preferred_frequency %}
                                        {{ freq_request.preferred_frequency }} MHz
                                    {% else %}
                                        Non spécifiée
                                    {% endif %}
                                </p>
                                <p><strong>Justification :</strong> {{ freq_request.justification }}</p>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h6>Validation technique :</h6>
                            <div class="bg-light p-3 rounded">
                                <p><strong>Validé par :</strong> {{ freq_request.technical_validator.get_full_name }}</p>
                                <p><strong>Date :</strong> {{ freq_request.technical_validation_date|date:"d/m/Y H:i" }}</p>
                                <p><strong>Commentaires :</strong> {{ freq_request.technical_comments }}</p>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h6>Décision finale :</h6>
                            <div class="bg-light p-3 rounded">
                                <p><strong>Décision par :</strong> {{ freq_request.official_validator.get_full_name }}</p>
                                <p><strong>Date :</strong> {{ freq_request.official_validation_date|date:"d/m/Y H:i" }}</p>
                                <p><strong>Commentaires :</strong> {{ freq_request.official_comments }}</p>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="frequency_id" class="form-label">Fréquence à attribuer</label>
                            <select class="form-select" id="frequency_id" name="frequency_id">
                                <option value="">-- Sélectionner une fréquence --</option>
                                {% for frequency in available_frequencies %}
                                <option value="{{ frequency.id }}">
                                    {{ frequency.frequency_value }} MHz ({{ frequency.reference }})
                                </option>
                                {% empty %}
                                <option value="" disabled>Aucune fréquence disponible dans cette bande</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                Sélectionnez une fréquence disponible à attribuer, ou laissez vide pour marquer comme attribuée sans fréquence spécifique.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="comments" class="form-label">Commentaires d'attribution</label>
                            <textarea class="form-control" id="comments" name="comments" rows="4" 
                                      placeholder="Commentaires sur l'attribution..."></textarea>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'requests:detail' freq_request.id %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-link me-2"></i>Attribuer Fréquence
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Informations</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Statut actuel</h6>
                        <span class="badge {{ freq_request.get_status_badge_class }}">
                            {{ freq_request.get_status_display }}
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Workflow</h6>
                        <ol class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Demande soumise
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Validation technique
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Décision finale
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-right text-warning me-2"></i>
                                <strong>Attribution</strong>
                            </li>
                        </ol>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Créer une nouvelle fréquence</h6>
                        <p class="small">Si aucune fréquence existante ne convient, vous pouvez en créer une nouvelle.</p>
                        <a href="{% url 'frequencies:create' %}" class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="fas fa-plus me-2"></i>Nouvelle Fréquence
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
