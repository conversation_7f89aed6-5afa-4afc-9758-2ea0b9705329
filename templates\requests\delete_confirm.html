{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Confirmation de Suppression
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h5 class="alert-heading">
                            <i class="fas fa-warning me-2"></i>
                            Attention !
                        </h5>
                        <p class="mb-0">
                            Vous êtes sur le point de supprimer définitivement cette demande. 
                            Cette action est <strong>irréversible</strong> et toutes les données associées seront perdues.
                        </p>
                    </div>

                    <!-- Informations de la demande -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">Référence</h6>
                            <p class="fw-bold">{{ freq_request.reference }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Demandeur</h6>
                            <p>{{ freq_request.requester.get_full_name }}</p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">Organe Demandeur</h6>
                            <p>
                                {% if freq_request.requesting_organe %}
                                    {{ freq_request.requesting_organe.name }}
                                {% elif freq_request.requesting_organe_text %}
                                    {{ freq_request.requesting_organe_text }}
                                {% else %}
                                    Non spécifié
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Statut</h6>
                            <p>
                                <span class="badge bg-{{ freq_request.get_status_badge_class }}">
                                    {{ freq_request.get_status_display }}
                                </span>
                            </p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">Type de Demande</h6>
                            <p>{{ freq_request.get_request_type_display }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Priorité</h6>
                            <p>
                                <span class="badge bg-{{ freq_request.get_priority_badge_class }}">
                                    {{ freq_request.get_priority_display }}
                                </span>
                            </p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-muted">Justification</h6>
                            <p>{{ freq_request.justification|default:"Aucune justification fournie" }}</p>
                        </div>
                    </div>

                    <!-- Boutons d'action -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'requests:detail' freq_request.id %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Annuler
                        </a>
                        
                        <form method="post" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>
                                Confirmer la Suppression
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Confirmation supplémentaire avant soumission
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!confirm('Êtes-vous absolument certain de vouloir supprimer cette demande ? Cette action ne peut pas être annulée.')) {
                e.preventDefault();
            }
        });
    }
});
</script>
{% endblock %}
