{% extends 'base.html' %}
{% load static %}

{% block title %}{{ request.reference }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{% url 'requests:list' %}">Demandes</a></li>
            <li class="breadcrumb-item active">{{ request.reference }}</li>
        </ol>
    </nav>

    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-file-alt me-2 text-primary"></i>
                        Demande {{ request.reference }}
                    </h1>
                    <p class="text-muted mb-0">
                        <span class="badge {{ request.get_status_badge_class }}">
                            {{ request.get_status_display }}
                        </span>
                        <span class="badge {{ request.get_priority_badge_class }} ms-2">
                            {{ request.get_priority_display }}
                        </span>
                    </p>
                </div>
                <div>
                    {% if request.status == 'draft' and request.requester == user %}
                    <a href="{% url 'requests:edit' request.id %}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                    <a href="{% url 'requests:submit' request.id %}" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>Soumettre
                    </a>
                    {% endif %}
                    
                    {% if request.can_be_validated_technically and user.role == 'chef_section' %}
                    <a href="{% url 'requests:validate-technical' request.id %}" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>Valider Techniquement
                    </a>
                    {% endif %}

                    {% if request.can_be_validated_officially and user.role == 'chef_division' %}
                    <a href="{% url 'requests:validate_official' request.id %}" class="btn btn-warning">
                        <i class="fas fa-stamp me-2"></i>Décision Finale
                    </a>
                    {% endif %}

                    {% if request.can_be_assigned and user.role == 'odr_gestionnaire' %}
                    <a href="{% url 'requests:assign' request.id %}" class="btn btn-primary">
                        <i class="fas fa-link me-2"></i>Attribuer Fréquence
                    </a>
                    {% endif %}

                    {% comment %} Boutons pour Admin {% endcomment %}
                    {% if user.role == 'admin' %}
                        {% if request.can_be_validated_technically %}
                        <a href="{% url 'requests:validate-technical' request.id %}" class="btn btn-success btn-sm">
                            <i class="fas fa-check me-2"></i>Validation Technique
                        </a>
                        {% endif %}
                        {% if request.can_be_validated_officially %}
                        <a href="{% url 'requests:validate_official' request.id %}" class="btn btn-warning btn-sm">
                            <i class="fas fa-stamp me-2"></i>Décision Finale
                        </a>
                        {% endif %}
                        {% if request.can_be_assigned %}
                        <a href="{% url 'requests:assign' request.id %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-link me-2"></i>Attribution
                        </a>
                        {% endif %}
                    {% endif %}
                    
                    <a href="{% url 'requests:list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Informations principales -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations de la Demande
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">Référence:</th>
                                    <td><strong>{{ request.reference }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Type de demande:</th>
                                    <td>{{ request.get_request_type_display }}</td>
                                </tr>
                                <tr>
                                    <th>Priorité:</th>
                                    <td>
                                        <span class="badge {{ request.get_priority_badge_class }}">
                                            {{ request.get_priority_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Statut:</th>
                                    <td>
                                        <span class="badge {{ request.get_status_badge_class }}">
                                            {{ request.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Bande demandée:</th>
                                    <td>
                                        <span class="badge freq-{{ request.frequency_band.name|lower }}">
                                            {{ request.frequency_band.name }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">Demandeur:</th>
                                    <td>{{ request.requester.get_full_name }}</td>
                                </tr>
                                <tr>
                                    <th>Unité Demandeur:</th>
                                    <td>
                                        {% if request.requesting_organe %}
                                            <a href="{% url 'frequencies:organe_detail' request.requesting_organe.id %}"
                                               class="text-decoration-none">
                                                {{ request.requesting_organe.name }}
                                            </a>
                                        {% elif request.requesting_organe_text %}
                                            <span class="badge bg-info">{{ request.requesting_organe_text }}</span>
                                        {% else %}
                                            <span class="text-muted">Non spécifié</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Date de création:</th>
                                    <td>{{ request.created_at|date:"d/m/Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <th>Date de soumission:</th>
                                    <td>
                                        {% if request.submitted_at %}
                                            {{ request.submitted_at|date:"d/m/Y H:i" }}
                                        {% else %}
                                            <span class="text-muted">Non soumise</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Temps de traitement:</th>
                                    <td>
                                        {% if request.get_processing_time %}
                                            {{ request.get_processing_time_display }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Spécifications techniques -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h me-2"></i>Spécifications Techniques
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">Bande de fréquence:</th>
                                    <td>
                                        <span class="badge freq-{{ request.frequency_band.name|lower }}">
                                            {{ request.frequency_band.name }}
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            {{ request.frequency_band.min_frequency }} - 
                                            {{ request.frequency_band.max_frequency }} MHz
                                        </small>
                                    </td>
                                </tr>
                                {% if request.preferred_frequency %}
                                <tr>
                                    <th>Fréquence préférée:</th>
                                    <td>{{ request.preferred_frequency }} MHz</td>
                                </tr>
                                {% endif %}
                                {% if request.frequency_range_min and request.frequency_range_max %}
                                <tr>
                                    <th>Plage acceptable:</th>
                                    <td>{{ request.frequency_range_min }} - {{ request.frequency_range_max }} MHz</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                {% if request.power_requested %}
                                <tr>
                                    <th width="40%">Puissance demandée:</th>
                                    <td>{{ request.power_requested }} W</td>
                                </tr>
                                {% endif %}
                                {% if request.coverage_area %}
                                <tr>
                                    <th>Zone de couverture:</th>
                                    <td>{{ request.coverage_area }} km</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>Date de début:</th>
                                    <td>{{ request.requested_start_date|date:"d/m/Y" }}</td>
                                </tr>
                                {% if request.requested_end_date %}
                                <tr>
                                    <th>Date de fin:</th>
                                    <td>{{ request.requested_end_date|date:"d/m/Y" }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Justification -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>Justification
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Objet de la demande:</h6>
                    <p>{{ request.purpose }}</p>
                    
                    <h6>Justification détaillée:</h6>
                    <div class="bg-light p-3 rounded mb-3">
                        {{ request.justification|linebreaks }}
                    </div>
                    
                    {% if request.operational_context %}
                    <h6>Contexte opérationnel:</h6>
                    <div class="bg-light p-3 rounded">
                        {{ request.operational_context|linebreaks }}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Commentaires -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-comments me-2"></i>Commentaires
                    </h5>
                </div>
                <div class="card-body">
                    {% if request.comments.all %}
                    <div class="comments-list">
                        {% for comment in request.comments.all %}
                        <div class="comment-item mb-3 {% if comment.is_internal %}bg-light{% endif %} p-3 rounded">
                            <div class="d-flex justify-content-between">
                                <h6>{{ comment.author.get_full_name }}</h6>
                                <small class="text-muted">{{ comment.created_at|date:"d/m/Y H:i" }}</small>
                            </div>
                            <p class="mb-0">{{ comment.content }}</p>
                            {% if comment.is_internal %}
                            <div class="mt-2">
                                <span class="badge bg-secondary">Commentaire interne</span>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center">Aucun commentaire pour le moment.</p>
                    {% endif %}
                    
                    <form method="post" action="#" class="mt-3">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="comment" class="form-label">Ajouter un commentaire:</label>
                            <textarea class="form-control" id="comment" name="comment" rows="3" required></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="is_internal" name="is_internal">
                            <label class="form-check-label" for="is_internal">
                                Commentaire interne (visible uniquement par le personnel)
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Envoyer
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Statut de la demande -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">Statut de la Demande</h6>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Création</span>
                            <span class="badge bg-success rounded-pill">
                                <i class="fas fa-check"></i>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Soumission</span>
                            {% if request.submitted_at %}
                            <span class="badge bg-success rounded-pill">
                                <i class="fas fa-check"></i>
                            </span>
                            {% else %}
                            <span class="badge bg-secondary rounded-pill">
                                <i class="fas fa-hourglass"></i>
                            </span>
                            {% endif %}
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Validation Technique</span>
                            {% if request.technical_validation_date %}
                            <span class="badge bg-success rounded-pill">
                                <i class="fas fa-check"></i>
                            </span>
                            {% elif request.status == 'submitted' or request.status == 'under_review' %}
                            <span class="badge bg-warning rounded-pill">
                                <i class="fas fa-spinner"></i>
                            </span>
                            {% else %}
                            <span class="badge bg-secondary rounded-pill">
                                <i class="fas fa-hourglass"></i>
                            </span>
                            {% endif %}
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Validation Officielle</span>
                            {% if request.official_validation_date %}
                            <span class="badge bg-success rounded-pill">
                                <i class="fas fa-check"></i>
                            </span>
                            {% elif request.status == 'technical_validation' %}
                            <span class="badge bg-warning rounded-pill">
                                <i class="fas fa-spinner"></i>
                            </span>
                            {% else %}
                            <span class="badge bg-secondary rounded-pill">
                                <i class="fas fa-hourglass"></i>
                            </span>
                            {% endif %}
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Attribution</span>
                            {% if request.assigned_frequency %}
                            <span class="badge bg-success rounded-pill">
                                <i class="fas fa-check"></i>
                            </span>
                            {% elif request.status == 'official_validation' %}
                            <span class="badge bg-warning rounded-pill">
                                <i class="fas fa-spinner"></i>
                            </span>
                            {% else %}
                            <span class="badge bg-secondary rounded-pill">
                                <i class="fas fa-hourglass"></i>
                            </span>
                            {% endif %}
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Fréquence attribuée -->
            {% if request.assigned_frequency %}
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-check-circle me-2"></i>Fréquence Attribuée
                    </h6>
                </div>
                <div class="card-body">
                    <h5 class="mb-3">{{ request.assigned_frequency.frequency_value }} MHz</h5>
                    <p class="mb-2">
                        <strong>Référence:</strong> 
                        <a href="{% url 'frequencies:detail' request.assigned_frequency.id %}" class="text-decoration-none">
                            {{ request.assigned_frequency.reference }}
                        </a>
                    </p>
                    <p class="mb-2">
                        <strong>Bande:</strong> 
                        <span class="badge freq-{{ request.assigned_frequency.band.name|lower }}">
                            {{ request.assigned_frequency.band.name }}
                        </span>
                    </p>
                    {% if request.assigned_frequency.power_max %}
                    <p class="mb-2"><strong>Puissance max:</strong> {{ request.assigned_frequency.power_max }} W</p>
                    {% endif %}
                    {% if request.assigned_frequency.coverage_radius %}
                    <p class="mb-2"><strong>Couverture:</strong> {{ request.assigned_frequency.coverage_radius }} km</p>
                    {% endif %}
                    <p class="mb-0">
                        <strong>Date d'attribution:</strong> 
                        {{ request.assigned_frequency.assignment_date|date:"d/m/Y" }}
                    </p>
                </div>
            </div>
            {% endif %}

            <!-- Validations -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">Validations</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Validation Technique</h6>
                        {% if request.technical_validation_date %}
                        <p class="mb-1">
                            <strong>Validé par:</strong> {{ request.technical_validator.get_full_name }}
                        </p>
                        <p class="mb-1">
                            <strong>Date:</strong> {{ request.technical_validation_date|date:"d/m/Y H:i" }}
                        </p>
                        {% if request.technical_comments %}
                        <p class="mb-0">
                            <strong>Commentaires:</strong><br>
                            <small>{{ request.technical_comments }}</small>
                        </p>
                        {% endif %}
                        {% else %}
                        <p class="text-muted">En attente de validation technique</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <h6>Validation Officielle</h6>
                        {% if request.official_validation_date %}
                        <p class="mb-1">
                            <strong>Validé par:</strong> {{ request.official_validator.get_full_name }}
                        </p>
                        <p class="mb-1">
                            <strong>Date:</strong> {{ request.official_validation_date|date:"d/m/Y H:i" }}
                        </p>
                        {% if request.official_comments %}
                        <p class="mb-0">
                            <strong>Commentaires:</strong><br>
                            <small>{{ request.official_comments }}</small>
                        </p>
                        {% endif %}
                        {% else %}
                        <p class="text-muted">En attente de validation officielle</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Pièces jointes -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Pièces Jointes</h6>
                </div>
                <div class="card-body">
                    {% if request.attachments.all %}
                    <ul class="list-group list-group-flush">
                        {% for attachment in request.attachments.all %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-file me-2"></i>
                                <a href="{{ attachment.file.url }}" target="_blank">
                                    {{ attachment.filename }}
                                </a>
                                {% if attachment.description %}
                                <br><small class="text-muted">{{ attachment.description }}</small>
                                {% endif %}
                            </div>
                            <small class="text-muted">{{ attachment.uploaded_at|date:"d/m/Y" }}</small>
                        </li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <p class="text-muted text-center">Aucune pièce jointe</p>
                    {% endif %}
                    
                    <form method="post" action="#" enctype="multipart/form-data" class="mt-3">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="file" class="form-label">Ajouter une pièce jointe:</label>
                            <input class="form-control" type="file" id="file" name="file" required>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description:</label>
                            <input type="text" class="form-control" id="description" name="description">
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>Télécharger
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
