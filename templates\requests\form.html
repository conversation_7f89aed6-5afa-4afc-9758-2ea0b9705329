{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{% url 'requests:list' %}">Demandes</a></li>
            <li class="breadcrumb-item active">{{ title }}</li>
        </ol>
    </nav>

    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-file-alt me-2 text-primary"></i>
                        {{ title }}
                    </h1>
                    <p class="text-muted mb-0">Formulaire de demande de fréquence radio</p>
                </div>
                <div>
                    <a href="{% url 'requests:list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>Formulaire de Demande
                    </h5>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                    <div class="alert alert-danger" role="alert">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Erreurs de validation :</h6>
                        <ul class="mb-0">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                <li>{{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        {% crispy form %}
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Aide contextuelle -->
    <div class="row justify-content-center mt-4">
        <div class="col-lg-10">
            <div class="card bg-light">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Guide de Saisie
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Types de demandes :</h6>
                            <ul class="small">
                                <li><strong>Nouvelle attribution :</strong> Première demande de fréquence</li>
                                <li><strong>Renouvellement :</strong> Prolongation d'une attribution existante</li>
                                <li><strong>Modification :</strong> Changement des paramètres d'une fréquence</li>
                                <li><strong>Attribution temporaire :</strong> Usage ponctuel ou exercice</li>
                            </ul>
                            
                            <h6>Priorités :</h6>
                            <ul class="small">
                                <li><strong>Urgente :</strong> Besoin opérationnel immédiat</li>
                                <li><strong>Haute :</strong> Mission prioritaire</li>
                                <li><strong>Normale :</strong> Besoin standard</li>
                                <li><strong>Basse :</strong> Besoin non critique</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Bandes de fréquences :</h6>
                            <ul class="small">
                                <li><strong>HF :</strong> 3-30 MHz - Longue portée</li>
                                <li><strong>VHF :</strong> 30-300 MHz - Portée moyenne</li>
                                <li><strong>UHF :</strong> 300-3000 MHz - Courte portée</li>
                                <li><strong>SHF :</strong> 3-30 GHz - Très courte portée</li>
                            </ul>
                            
                            <h6>Conseils de rédaction :</h6>
                            <ul class="small">
                                <li>Soyez précis dans la justification</li>
                                <li>Indiquez le contexte opérationnel si applicable</li>
                                <li>Spécifiez les contraintes techniques</li>
                                <li>Mentionnez les alternatives acceptables</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3" role="alert">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Astuce :</strong> Vous pouvez sauvegarder votre demande en brouillon et la compléter plus tard, 
                        ou l'enregistrer et la soumettre directement pour traitement.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Validation côté client pour les fréquences
    $('#id_preferred_frequency, #id_frequency_range_min, #id_frequency_range_max, #id_frequency_band').on('change', function() {
        validateFrequencyRanges();
    });
    
    // Validation des dates
    $('#id_requested_start_date, #id_requested_end_date').on('change', function() {
        validateDates();
    });
    
    // Auto-complétion de la plage de fréquences basée sur la fréquence préférée
    $('#id_preferred_frequency').on('change', function() {
        const preferredFreq = parseFloat($(this).val());
        if (preferredFreq) {
            const minField = $('#id_frequency_range_min');
            const maxField = $('#id_frequency_range_max');
            
            if (!minField.val()) {
                minField.val((preferredFreq - 1).toFixed(3));
            }
            if (!maxField.val()) {
                maxField.val((preferredFreq + 1).toFixed(3));
            }
        }
    });
});

function validateFrequencyRanges() {
    const bandSelect = $('#id_frequency_band');
    const selectedBand = bandSelect.find('option:selected');
    const preferredFreq = parseFloat($('#id_preferred_frequency').val());
    const minFreq = parseFloat($('#id_frequency_range_min').val());
    const maxFreq = parseFloat($('#id_frequency_range_max').val());
    
    // Définir les plages de bandes
    const bandRanges = {
        'HF': [3, 30],
        'VHF': [30, 300],
        'UHF': [300, 3000],
        'SHF': [3000, 30000]
    };
    
    if (selectedBand.length) {
        const bandName = selectedBand.text().split(' ')[0];
        const range = bandRanges[bandName];
        
        if (range) {
            // Valider la fréquence préférée
            if (preferredFreq && (preferredFreq < range[0] || preferredFreq > range[1])) {
                showFieldError('id_preferred_frequency', `Doit être entre ${range[0]} et ${range[1]} MHz`);
            } else {
                clearFieldError('id_preferred_frequency');
            }
            
            // Valider la plage minimale
            if (minFreq && (minFreq < range[0] || minFreq > range[1])) {
                showFieldError('id_frequency_range_min', `Doit être entre ${range[0]} et ${range[1]} MHz`);
            } else {
                clearFieldError('id_frequency_range_min');
            }
            
            // Valider la plage maximale
            if (maxFreq && (maxFreq < range[0] || maxFreq > range[1])) {
                showFieldError('id_frequency_range_max', `Doit être entre ${range[0]} et ${range[1]} MHz`);
            } else {
                clearFieldError('id_frequency_range_max');
            }
        }
    }
    
    // Valider que min < max
    if (minFreq && maxFreq && minFreq >= maxFreq) {
        showFieldError('id_frequency_range_max', 'Doit être supérieure à la fréquence minimale');
    }
}

function validateDates() {
    const startDate = new Date($('#id_requested_start_date').val());
    const endDate = new Date($('#id_requested_end_date').val());
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Valider que la date de début n'est pas dans le passé
    if (startDate < today) {
        showFieldError('id_requested_start_date', 'Ne peut pas être dans le passé');
    } else {
        clearFieldError('id_requested_start_date');
    }
    
    // Valider que la date de fin est après la date de début
    if (startDate && endDate && startDate >= endDate) {
        showFieldError('id_requested_end_date', 'Doit être postérieure à la date de début');
    } else {
        clearFieldError('id_requested_end_date');
    }
}

function showFieldError(fieldId, message) {
    const field = $('#' + fieldId);
    field.addClass('is-invalid');
    
    // Supprimer l'ancien message d'erreur
    field.siblings('.invalid-feedback').remove();
    
    // Ajouter le nouveau message
    field.after(`<div class="invalid-feedback">${message}</div>`);
}

function clearFieldError(fieldId) {
    const field = $('#' + fieldId);
    field.removeClass('is-invalid');
    field.siblings('.invalid-feedback').remove();
}
</script>
{% endblock %}
