{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-file-alt me-2 text-primary"></i>
                {{ title }}
            </h1>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Formulaire de Demande Simplifié</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="requesting_organe_text" class="form-label">Unité Demandeur *</label>
                                    <input type="text" class="form-control" id="requesting_organe_text" 
                                           name="requesting_organe_text" required
                                           placeholder="Nom de l'unité (saisie libre)">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="request_type" class="form-label">Type de Demande *</label>
                                    <select class="form-select" id="request_type" name="request_type" required>
                                        <option value="">Sélectionner</option>
                                        <option value="new_assignment">Nouvelle attribution</option>
                                        <option value="renewal">Renouvellement</option>
                                        <option value="modification">Modification</option>
                                        <option value="temporary">Attribution temporaire</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="priority" class="form-label">Priorité *</label>
                                    <select class="form-select" id="priority" name="priority" required>
                                        <option value="">Sélectionner</option>
                                        <option value="urgent">Urgente</option>
                                        <option value="high">Haute</option>
                                        <option value="normal">Normale</option>
                                        <option value="low">Basse</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="frequency_band" class="form-label">Bande de Fréquence *</label>
                                    <select class="form-select" id="frequency_band" name="frequency_band" required>
                                        <option value="">Sélectionner</option>
                                        <option value="1">HF (3-30 MHz)</option>
                                        <option value="2">VHF (30-300 MHz)</option>
                                        <option value="3">UHF (300-3000 MHz)</option>
                                        <option value="4">SHF (3-30 GHz)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="preferred_frequency" class="form-label">Fréquence Préférée (MHz)</label>
                                    <input type="number" class="form-control" id="preferred_frequency" 
                                           name="preferred_frequency" step="0.001" placeholder="Ex: 145.500">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="power_requested" class="form-label">Puissance (W)</label>
                                    <input type="number" class="form-control" id="power_requested" 
                                           name="power_requested" placeholder="Ex: 50">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="coverage_area" class="form-label">Zone Couverture (km)</label>
                                    <input type="number" class="form-control" id="coverage_area" 
                                           name="coverage_area" placeholder="Ex: 25">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="purpose" class="form-label">Objet de la Demande *</label>
                            <input type="text" class="form-control" id="purpose" name="purpose" required
                                   placeholder="Résumé en quelques mots">
                        </div>
                        
                        <div class="mb-3">
                            <label for="justification" class="form-label">Justification Complète *</label>
                            <textarea class="form-control" id="justification" name="justification" 
                                      rows="5" required
                                      placeholder="Justification détaillée incluant le contexte opérationnel, l'urgence, etc."></textarea>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'requests:list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <div>
                                <button type="submit" name="submit" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-save me-2"></i>Enregistrer Brouillon
                                </button>
                                <button type="submit" name="submit_and_send" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>Enregistrer et Soumettre
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
