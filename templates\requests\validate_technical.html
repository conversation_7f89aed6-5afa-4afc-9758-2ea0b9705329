{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-check-circle me-2 text-success"></i>
                {{ title }}
            </h1>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Validation Technique</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Chef de Section :</strong> Vous êtes sur le point de valider techniquement cette demande.
                        Vérifiez que les spécifications techniques sont conformes et réalisables.
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <h6>Résum<PERSON> de la demande :</h6>
                            <div class="bg-light p-3 rounded">
                                <p><strong>Référence :</strong> {{ freq_request.reference }}</p>
                                <p><strong>Demandeur :</strong> {{ freq_request.requesting_organe_text }}</p>
                                <p><strong>Type :</strong> {{ freq_request.get_request_type_display }}</p>
                                <p><strong>Bande :</strong> {{ freq_request.frequency_band.name }}</p>
                                <p><strong>Fréquence préférée :</strong> 
                                    {% if freq_request.preferred_frequency %}
                                        {{ freq_request.preferred_frequency }} MHz
                                    {% else %}
                                        Non spécifiée
                                    {% endif %}
                                </p>
                                <p><strong>Puissance :</strong> 
                                    {% if freq_request.power_requested %}
                                        {{ freq_request.power_requested }} W
                                    {% else %}
                                        Non spécifiée
                                    {% endif %}
                                </p>
                                <p><strong>Justification :</strong> {{ freq_request.justification }}</p>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="comments" class="form-label">Commentaires de validation technique</label>
                            <textarea class="form-control" id="comments" name="comments" rows="4" 
                                      placeholder="Commentaires sur la faisabilité technique, recommandations, etc."></textarea>
                            <div class="form-text">
                                Ces commentaires seront visibles par le Chef de Division et l'ODR Gestionnaire.
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'requests:detail' freq_request.id %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-2"></i>Valider Techniquement
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Informations</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Statut actuel</h6>
                        <span class="badge {{ freq_request.get_status_badge_class }}">
                            {{ freq_request.get_status_display }}
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Workflow</h6>
                        <ol class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Demande soumise
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-right text-warning me-2"></i>
                                <strong>Validation technique</strong>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-clock text-muted me-2"></i>
                                Décision finale
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-clock text-muted me-2"></i>
                                Attribution
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
